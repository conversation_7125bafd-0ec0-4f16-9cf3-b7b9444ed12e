// Knowledge Test Panel
// A simple test component to verify our knowledge handlers work correctly

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { knowledgeHandlers } from '@/lib/knowledgeHandlers';
import { FileText, Users, Bot, Play, CheckCircle, XCircle } from 'lucide-react';

interface TestResult {
  name: string;
  success: boolean;
  message: string;
  data?: any;
}

export function KnowledgeTestPanel() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    const results: TestResult[] = [];

    // Test 1: Initialize knowledge features
    try {
      const initResult = await knowledgeHandlers.initialize();
      results.push({
        name: 'Initialize Knowledge Features',
        success: initResult.success,
        message: initResult.success ? 'Successfully initialized' : initResult.error?.message || 'Failed',
        data: initResult.data
      });
    } catch (error) {
      results.push({
        name: 'Initialize Knowledge Features',
        success: false,
        message: 'Exception thrown: ' + (error as Error).message
      });
    }

    // Test 2: Get documents
    try {
      const docsResult = await knowledgeHandlers.documents.getDocuments();
      results.push({
        name: 'Get Documents',
        success: docsResult.success,
        message: docsResult.success 
          ? `Found ${docsResult.data?.length || 0} documents` 
          : docsResult.error?.message || 'Failed',
        data: docsResult.data
      });
    } catch (error) {
      results.push({
        name: 'Get Documents',
        success: false,
        message: 'Exception thrown: ' + (error as Error).message
      });
    }

    // Test 3: Get social links
    try {
      const socialResult = await knowledgeHandlers.socialLinks.getSocialLinks();
      results.push({
        name: 'Get Social Links',
        success: socialResult.success,
        message: socialResult.success 
          ? 'Successfully retrieved social links' 
          : socialResult.error?.message || 'Failed',
        data: socialResult.data
      });
    } catch (error) {
      results.push({
        name: 'Get Social Links',
        success: false,
        message: 'Exception thrown: ' + (error as Error).message
      });
    }

    // Test 4: Get AI personality
    try {
      const personalityResult = await knowledgeHandlers.aiPersonality.getPersonality();
      results.push({
        name: 'Get AI Personality',
        success: personalityResult.success,
        message: personalityResult.success 
          ? 'Successfully retrieved AI personality settings' 
          : personalityResult.error?.message || 'Failed',
        data: personalityResult.data
      });
    } catch (error) {
      results.push({
        name: 'Get AI Personality',
        success: false,
        message: 'Exception thrown: ' + (error as Error).message
      });
    }

    // Test 5: Validate social links
    try {
      const testLinks = {
        twitter: 'https://twitter.com/test',
        instagram: 'https://instagram.com/test',
        linkedin: 'https://linkedin.com/in/test',
        github: 'https://github.com/test',
        website: 'https://example.com'
      };
      
      const validation = knowledgeHandlers.socialLinks.validateSocialLinks(testLinks);
      results.push({
        name: 'Validate Social Links',
        success: validation.valid,
        message: validation.valid 
          ? 'All social links are valid' 
          : `Validation errors: ${validation.errors.join(', ')}`,
        data: validation
      });
    } catch (error) {
      results.push({
        name: 'Validate Social Links',
        success: false,
        message: 'Exception thrown: ' + (error as Error).message
      });
    }

    // Test 6: Validate AI personality
    try {
      const testPersonality = {
        tone: 'friendly',
        style: 'conversational',
        personality: 'A helpful and engaging AI assistant',
        response_length: 'medium',
        custom_instructions: 'Be helpful and concise'
      };
      
      const validation = knowledgeHandlers.aiPersonality.validatePersonality(testPersonality);
      results.push({
        name: 'Validate AI Personality',
        success: validation.valid,
        message: validation.valid 
          ? 'AI personality settings are valid' 
          : `Validation errors: ${validation.errors.join(', ')}`,
        data: validation
      });
    } catch (error) {
      results.push({
        name: 'Validate AI Personality',
        success: false,
        message: 'Exception thrown: ' + (error as Error).message
      });
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const getTestIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const successCount = testResults.filter(r => r.success).length;
  const totalTests = testResults.length;

  return (
    <Card className="glass-card w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Bot className="w-5 h-5" />
          <span>Knowledge Features Test Panel</span>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Test the Upload Documents, Social Links, and AI Personality handlers
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <Button
            onClick={runTests}
            disabled={isRunning}
            className="brand-gradient hover:opacity-90 text-white"
          >
            <Play className="w-4 h-4 mr-2" />
            {isRunning ? 'Running Tests...' : 'Run Tests'}
          </Button>
          
          {testResults.length > 0 && (
            <Badge variant={successCount === totalTests ? 'default' : 'destructive'}>
              {successCount}/{totalTests} tests passed
            </Badge>
          )}
        </div>

        {testResults.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Test Results:</h4>
            {testResults.map((result, index) => (
              <div
                key={index}
                className="flex items-start space-x-3 p-3 glass-button rounded-lg"
              >
                {getTestIcon(result.success)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm">{result.name}</span>
                    <Badge variant={result.success ? 'default' : 'destructive'} className="text-xs">
                      {result.success ? 'PASS' : 'FAIL'}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">{result.message}</p>
                  {result.data && (
                    <details className="mt-2">
                      <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                        View data
                      </summary>
                      <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto max-h-32">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="mt-6 p-4 glass-button rounded-lg">
          <h4 className="font-medium mb-2 flex items-center space-x-2">
            <FileText className="w-4 h-4" />
            <span>Features Overview</span>
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h5 className="font-medium text-primary">Upload Documents</h5>
              <ul className="text-muted-foreground mt-1 space-y-1">
                <li>• File upload to Supabase storage</li>
                <li>• Document metadata management</li>
                <li>• Content preview extraction</li>
                <li>• File validation and processing</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-primary">Social Links</h5>
              <ul className="text-muted-foreground mt-1 space-y-1">
                <li>• Profile social links management</li>
                <li>• URL validation and normalization</li>
                <li>• Real-time preview</li>
                <li>• Database persistence</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-primary">AI Personality</h5>
              <ul className="text-muted-foreground mt-1 space-y-1">
                <li>• Tone and style configuration</li>
                <li>• Custom personality settings</li>
                <li>• Response length preferences</li>
                <li>• Chat behavior customization</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
