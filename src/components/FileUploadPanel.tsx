
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, File, X, FileText, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { documentService, type DocumentMetadata } from '@/lib/documentService';
import { toast } from 'sonner';

interface FileUploadPanelProps {
  uploadedFiles: File[];
  onFilesChange: (files: File[]) => void;
}

export function FileUploadPanel({ uploadedFiles, onFilesChange }: FileUploadPanelProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [documents, setDocuments] = useState<DocumentMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { t } = useTranslation();

  // Load existing documents on component mount
  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    setIsLoading(true);
    try {
      const result = await documentService.getUserDocuments();
      if (result.success && result.data) {
        setDocuments(result.data);
      } else {
        console.error('Failed to load documents:', result.error);
      }
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || isUploading) return;

    const fileArray = Array.from(files);
    setIsUploading(true);

    try {
      // Upload each file
      for (const file of fileArray) {
        const result = await documentService.uploadDocument(file);

        if (result.success && result.data) {
          setDocuments(prev => [...prev, result.data!]);
          toast.success(`${file.name} uploaded successfully`);
        } else {
          toast.error(`Failed to upload ${file.name}: ${result.error?.message}`);
        }
      }

      // Update local state for compatibility
      onFilesChange([...uploadedFiles, ...fileArray]);
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('An unexpected error occurred during upload');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const removeFile = (index: number) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index);
    onFilesChange(newFiles);
  };

  const removeDocument = async (documentId: string) => {
    try {
      const result = await documentService.deleteDocument(documentId);

      if (result.success) {
        setDocuments(prev => prev.filter(doc => doc.id !== documentId));
        toast.success('Document deleted successfully');
      } else {
        toast.error(`Failed to delete document: ${result.error?.message}`);
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error('An unexpected error occurred while deleting');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="h-full"
    >
      <Card className="glass-panel h-full">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5" />
            <span>{t('fileUploadPanel.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              isDragOver 
                ? 'border-primary bg-primary/10' 
                : 'border-white/20 hover:border-white/40'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="w-8 h-8 mx-auto mb-3 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-2">
              {t('fileUploadPanel.drop')}
            </p>
            <Button
              variant="outline"
              className="glass-button"
              onClick={() => document.getElementById('file-upload')?.click()}
              disabled={isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                t('fileUploadPanel.chooseFiles')
              )}
            </Button>
            <input
              id="file-upload"
              type="file"
              multiple
              accept=".pdf,.txt,.md,.doc,.docx"
              className="hidden"
              onChange={(e) => handleFileUpload(e.target.files)}
              disabled={isUploading}
            />
          </div>

          {/* Documents List */}
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">Loading documents...</span>
            </div>
          ) : (
            <>
              {/* Uploaded Files (temporary local state) */}
              {uploadedFiles.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">{t('fileUploadPanel.uploadedFiles', { count: uploadedFiles.length })}</h4>
                  <div className="max-h-32 overflow-y-auto space-y-2">
                    {uploadedFiles.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 glass-button rounded-lg"
                      >
                        <div className="flex items-center space-x-2 flex-1 min-w-0">
                          <File className="w-4 h-4 text-primary flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{file.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {formatFileSize(file.size)}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          className="text-muted-foreground hover:text-destructive"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Stored Documents */}
              {documents.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Stored Documents ({documents.length})</h4>
                  <div className="max-h-48 overflow-y-auto space-y-2">
                    {documents.map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center justify-between p-2 glass-button rounded-lg"
                      >
                        <div className="flex items-center space-x-2 flex-1 min-w-0">
                          <div className="flex items-center space-x-1">
                            <File className="w-4 h-4 text-primary flex-shrink-0" />
                            {doc.processed ? (
                              <CheckCircle className="w-3 h-3 text-green-500" />
                            ) : (
                              <AlertCircle className="w-3 h-3 text-yellow-500" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{doc.original_name}</p>
                            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                              <span>{formatFileSize(doc.file_size)}</span>
                              {doc.tags && doc.tags.length > 0 && (
                                <span>• {doc.tags.join(', ')}</span>
                              )}
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeDocument(doc.id)}
                          className="text-muted-foreground hover:text-destructive"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}

          {/* Info */}
          <div className="p-3 glass-button rounded-lg">
            <h4 className="font-medium mb-2 text-sm">{t('fileUploadPanel.supportedFormats')}</h4>
            <p className="text-xs text-muted-foreground">
              {t('fileUploadPanel.formatDescription')}
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
