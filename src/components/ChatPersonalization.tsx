
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MessageCircle, Send, Bot, Save, Loader2, CheckCircle, RotateCcw } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { createAIService, defaultAIConfig } from '@/lib/aiService';
import { chatService } from '@/lib/chatService';
import { settingsService, type AIPersonalitySettings } from '@/lib/settingsService';
import { toast } from 'sonner';

export function ChatPersonalization() {
  const [personality, setPersonality] = useState('');
  const [greeting, setGreeting] = useState('');
  const [tone, setTone] = useState<AIPersonalitySettings['tone']>('friendly');
  const [style, setStyle] = useState<AIPersonalitySettings['style']>('conversational');
  const [responseLength, setResponseLength] = useState<AIPersonalitySettings['response_length']>('medium');
  const [customInstructions, setCustomInstructions] = useState('');
  const [testMessage, setTestMessage] = useState('');
  const [chatMessages, setChatMessages] = useState([
    { role: 'assistant', content: 'Hi! I\'m your AI assistant. Ask me anything!' }
  ]);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);
  const { t } = useTranslation();

  // Load existing AI personality settings on component mount
  useEffect(() => {
    loadAIPersonality();
  }, []);

  const loadAIPersonality = async () => {
    setIsLoading(true);
    try {
      const result = await settingsService.getAIPersonality();
      if (result.success && result.data) {
        const settings = result.data;
        setPersonality(settings.personality);
        setGreeting(settings.greeting_message || '');
        setTone(settings.tone);
        setStyle(settings.style);
        setResponseLength(settings.response_length);
        setCustomInstructions(settings.custom_instructions);
      } else {
        console.error('Failed to load AI personality:', result.error);
      }
    } catch (error) {
      console.error('Error loading AI personality:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!hasChanges || isSaving) return;

    setIsSaving(true);
    try {
      const personalitySettings: Partial<AIPersonalitySettings> = {
        personality,
        greeting_message: greeting,
        tone,
        style,
        response_length: responseLength,
        custom_instructions: customInstructions,
        knowledge_areas: ['general'] // Default for now
      };

      const result = await settingsService.updateAIPersonality(personalitySettings);

      if (result.success) {
        setHasChanges(false);
        toast.success('AI personality settings saved successfully');
      } else {
        toast.error(`Failed to save settings: ${result.error?.message}`);
      }
    } catch (error) {
      console.error('Save error:', error);
      toast.error('An unexpected error occurred while saving');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async () => {
    try {
      const result = await settingsService.resetToDefaults();
      if (result.success) {
        await loadAIPersonality();
        setHasChanges(false);
        toast.success('Settings reset to defaults');
      } else {
        toast.error('Failed to reset settings');
      }
    } catch (error) {
      console.error('Reset error:', error);
      toast.error('An unexpected error occurred while resetting');
    }
  };

  const markAsChanged = () => {
    setHasChanges(true);
  };

  const handleTestChat = async () => {
    if (!testMessage.trim()) return;

    const userMessage = { role: 'user', content: testMessage };
    setChatMessages(prev => [...prev, userMessage]);

    const ai = createAIService({
      ...defaultAIConfig,
      personality: {
        ...defaultAIConfig.personality,
        style: 'conversational',
        tone: 'friendly',
        response_length: 'short',
        traits: ['helpful', 'engaging'],
      }
    });

    // Minimal mock context for testing locally
    const ctxResult = await chatService.buildChatContext('test_session');
    const context = ctxResult.success && ctxResult.data ? ctxResult.data : {
      recent_messages: [],
      user_profile: { name: 'You' },
      conversation_history: { topics: [], sentiment_trend: 0, key_points: [] },
      session_info: { duration_minutes: 0, message_count: 0, last_interaction: new Date().toISOString() }
    } as any;

    const aiResult = await ai.generateResponse(testMessage, context);
    const reply = aiResult.success && aiResult.data ? aiResult.data.content : `Thanks for testing! You said: "${testMessage}".`;

    setChatMessages(prev => [...prev, { role: 'assistant', content: reply }]);
    setTestMessage('');
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      className="h-full"
    >
      <Card className="glass-panel h-full">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bot className="w-5 h-5" />
            <span>{t('chatPersonalization.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">Loading AI settings...</span>
            </div>
          ) : (
            <>
              {/* Personality Settings */}
              <div className="space-y-2">
                <Label htmlFor="personality">{t('chatPersonalization.personalityLabel')}</Label>
                <Textarea
                  id="personality"
                  placeholder={t('chatPersonalization.personalityPlaceholder')}
                  value={personality}
                  onChange={(e) => {
                    setPersonality(e.target.value);
                    markAsChanged();
                  }}
                  className="glass-button resize-none"
                  rows={3}
                  disabled={isSaving}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="greeting">{t('chatPersonalization.greetingLabel')}</Label>
                <Input
                  id="greeting"
                  placeholder={t('chatPersonalization.greetingPlaceholder')}
                  value={greeting}
                  onChange={(e) => {
                    setGreeting(e.target.value);
                    markAsChanged();
                  }}
                  className="glass-button"
                  disabled={isSaving}
                />
              </div>

              {/* Tone and Style Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tone">Tone</Label>
                  <Select
                    value={tone}
                    onValueChange={(value: AIPersonalitySettings['tone']) => {
                      setTone(value);
                      markAsChanged();
                    }}
                    disabled={isSaving}
                  >
                    <SelectTrigger className="glass-button">
                      <SelectValue placeholder="Select tone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="friendly">Friendly</SelectItem>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="casual">Casual</SelectItem>
                      <SelectItem value="formal">Formal</SelectItem>
                      <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="style">Style</Label>
                  <Select
                    value={style}
                    onValueChange={(value: AIPersonalitySettings['style']) => {
                      setStyle(value);
                      markAsChanged();
                    }}
                    disabled={isSaving}
                  >
                    <SelectTrigger className="glass-button">
                      <SelectValue placeholder="Select style" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="conversational">Conversational</SelectItem>
                      <SelectItem value="informative">Informative</SelectItem>
                      <SelectItem value="creative">Creative</SelectItem>
                      <SelectItem value="analytical">Analytical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="responseLength">Response Length</Label>
                <Select
                  value={responseLength}
                  onValueChange={(value: AIPersonalitySettings['response_length']) => {
                    setResponseLength(value);
                    markAsChanged();
                  }}
                  disabled={isSaving}
                >
                  <SelectTrigger className="glass-button">
                    <SelectValue placeholder="Select response length" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="short">Short</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="long">Long</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="customInstructions">Custom Instructions</Label>
                <Textarea
                  id="customInstructions"
                  placeholder="Add any specific instructions for your AI assistant..."
                  value={customInstructions}
                  onChange={(e) => {
                    setCustomInstructions(e.target.value);
                    markAsChanged();
                  }}
                  className="glass-button resize-none"
                  rows={2}
                  disabled={isSaving}
                />
              </div>

              {/* Action Buttons */}
              {hasChanges && (
                <div className="flex justify-between pt-4">
                  <Button
                    onClick={handleReset}
                    variant="outline"
                    disabled={isSaving}
                    className="glass-button"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset to Defaults
                  </Button>

                  <Button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="brand-gradient hover:opacity-90 text-white"
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Settings
                      </>
                    )}
                  </Button>
                </div>
              )}
            </>
          )}

          {/* Test Chat */}
          <div className="border-t border-white/10 pt-4">
            <h4 className="font-medium mb-3 flex items-center space-x-2">
              <MessageCircle className="w-4 h-4" />
              <span>{t('chatPersonalization.testTitle')}</span>
            </h4>
            
            <div className="glass-button rounded-lg p-3 h-32 overflow-y-auto space-y-2 mb-3">
              {chatMessages.map((message, index) => (
                <div
                  key={index}
                  className={`text-sm p-2 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-primary/20 text-right ml-4'
                      : 'bg-white/10 mr-4'
                  }`}
                >
                  {message.content}
                </div>
              ))}
            </div>

            <div className="flex space-x-2">
              <Input
                placeholder={t('chatPersonalization.testPlaceholder')}
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleTestChat()}
                className="glass-button flex-1"
              />
              <Button
                onClick={handleTestChat}
                size="sm"
                className="brand-gradient hover:opacity-90 text-white"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
