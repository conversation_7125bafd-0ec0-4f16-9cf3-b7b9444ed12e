import React, { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';

export function AuthErrorHandler() {
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    if (error) {
      console.error('Auth error detected:', { error, errorDescription });
      
      let title = t('toasts.authError');
      let description = errorDescription || error;

      // Handle specific error types
      switch (error) {
        case 'server_error':
          if (errorDescription?.includes('converting NULL to string')) {
            title = 'Database Error';
            description = 'Authentication database issue detected. Please try again or contact support.';
          } else {
            title = 'Server Error';
            description = 'A server error occurred during authentication. Please try again.';
          }
          break;
        case 'access_denied':
          title = 'Access Denied';
          description = 'You denied access to your account. Please try again if this was a mistake.';
          break;
        case 'invalid_request':
          title = 'Invalid Request';
          description = 'The authentication request was invalid. Please try again.';
          break;
        default:
          title = 'Authentication Error';
          description = errorDescription || 'An error occurred during authentication.';
      }

      toast({
        title,
        description,
        variant: 'destructive',
        duration: 10000, // Show longer for auth errors
      });

      // Clean up URL parameters
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('error');
      newSearchParams.delete('error_description');
      setSearchParams(newSearchParams, { replace: true });
    }
  }, [searchParams, setSearchParams, toast, t]);

  return null; // This component doesn't render anything
}
