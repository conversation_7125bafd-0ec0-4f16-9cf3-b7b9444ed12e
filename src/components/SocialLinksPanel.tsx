
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Twitter, Instagram, Linkedin, Github, Globe, Save, Loader2, CheckCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { profileService, type SocialLinks } from '@/lib/profileService';
import { toast } from 'sonner';

interface SocialLinksPanelProps {
  socialLinks: SocialLinks;
  onChange: (links: SocialLinks) => void;
}

export function SocialLinksPanel({ socialLinks, onChange }: SocialLinksPanelProps) {
  const { t } = useTranslation();
  const [localLinks, setLocalLinks] = useState<SocialLinks>(socialLinks);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);

  // Load existing social links on component mount
  useEffect(() => {
    loadSocialLinks();
  }, []);

  // Update local state when props change
  useEffect(() => {
    setLocalLinks(socialLinks);
  }, [socialLinks]);

  const loadSocialLinks = async () => {
    setIsLoading(true);
    try {
      const result = await profileService.getSocialLinks();
      if (result.success && result.data) {
        setLocalLinks(result.data);
        onChange(result.data);
      } else {
        console.error('Failed to load social links:', result.error);
      }
    } catch (error) {
      console.error('Error loading social links:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (platform: keyof SocialLinks, value: string) => {
    const updatedLinks = {
      ...localLinks,
      [platform]: value
    };
    setLocalLinks(updatedLinks);
    onChange(updatedLinks);
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!hasChanges || isSaving) return;

    setIsSaving(true);
    try {
      const result = await profileService.updateSocialLinks(localLinks);

      if (result.success) {
        setHasChanges(false);
        toast.success('Social links saved successfully');
      } else {
        toast.error(`Failed to save social links: ${result.error?.message}`);
      }
    } catch (error) {
      console.error('Save error:', error);
      toast.error('An unexpected error occurred while saving');
    } finally {
      setIsSaving(false);
    }
  };

  const socialPlatforms = [
    { key: 'twitter' as keyof SocialLinks, label: t('socialLinksPanel.twitter'), icon: Twitter, placeholder: t('socialLinksPanel.urlPlaceholder.twitter') },
    { key: 'instagram' as keyof SocialLinks, label: t('socialLinksPanel.instagram'), icon: Instagram, placeholder: t('socialLinksPanel.urlPlaceholder.instagram') },
    { key: 'linkedin' as keyof SocialLinks, label: t('socialLinksPanel.linkedin'), icon: Linkedin, placeholder: t('socialLinksPanel.urlPlaceholder.linkedin') },
    { key: 'github' as keyof SocialLinks, label: t('socialLinksPanel.github'), icon: Github, placeholder: t('socialLinksPanel.urlPlaceholder.github') },
    { key: 'website' as keyof SocialLinks, label: t('socialLinksPanel.website'), icon: Globe, placeholder: t('socialLinksPanel.urlPlaceholder.website') },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin" />
        <span className="ml-2 text-sm text-muted-foreground">Loading social links...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {socialPlatforms.map(({ key, label, icon: Icon, placeholder }) => (
        <div key={key} className="space-y-3">
          <Label htmlFor={key} className="body-md font-medium flex items-center gap-3">
            <Icon className="w-5 h-5 text-primary" />
            <span>{label}</span>
          </Label>
          <Input
            id={key}
            type="url"
            placeholder={placeholder}
            value={localLinks[key]}
            onChange={(e) => handleInputChange(key, e.target.value)}
            className="glass-button h-12 text-base"
            disabled={isSaving}
          />
        </div>
      ))}

      {/* Save Button */}
      {hasChanges && (
        <div className="flex justify-end pt-4">
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="brand-gradient hover:opacity-90 text-white"
          >
            {isSaving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      )}

      <div className="mt-8 p-4 glass-button rounded-2xl">
        <h4 className="body-lg font-medium mb-2 flex items-center gap-2">
          {t('socialLinksPanel.preview')}
          {!hasChanges && !isLoading && (
            <CheckCircle className="w-4 h-4 text-green-500" />
          )}
        </h4>
        <p className="body-md text-muted-foreground">
          {t('socialLinksPanel.previewDescription')}
        </p>

        {/* Preview Links */}
        <div className="mt-3 space-y-2">
          {socialPlatforms.map(({ key, label, icon: Icon }) => {
            const url = localLinks[key];
            if (!url) return null;

            return (
              <div key={key} className="flex items-center gap-2 text-sm">
                <Icon className="w-4 h-4 text-primary" />
                <span className="text-muted-foreground">{label}:</span>
                <a
                  href={url.startsWith('http') ? url : `https://${url}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline truncate"
                >
                  {url}
                </a>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
