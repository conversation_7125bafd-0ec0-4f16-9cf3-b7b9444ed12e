// Profile Service
// Handles user profile management including social links

import { supabase } from '@/integrations/supabase/client';

export interface SocialLinks {
  twitter: string;
  instagram: string;
  linkedin: string;
  github: string;
  website: string;
}

export interface ProfileData {
  id: string;
  username: string;
  display_name: string;
  bio: string;
  avatar_url: string;
  social_links: SocialLinks;
  is_public: boolean;
  created_at: string;
  updated_at?: string;
}

export interface ProfileUpdateResult {
  success: boolean;
  data?: ProfileData;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

export interface SocialLinksResult {
  success: boolean;
  data?: SocialLinks;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

class ProfileService {
  /**
   * Get current user's profile
   */
  async getCurrentProfile(): Promise<ProfileUpdateResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        return {
          success: false,
          error: {
            code: 'FETCH_ERROR',
            message: 'Failed to fetch profile',
            details: error
          }
        };
      }

      return {
        success: true,
        data: data as ProfileData
      };

    } catch (error) {
      console.error('Get profile error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Update user's social links
   */
  async updateSocialLinks(socialLinks: SocialLinks): Promise<SocialLinksResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      // Validate social links
      const validation = this.validateSocialLinks(socialLinks);
      if (!validation.valid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: validation.error || 'Invalid social links'
          }
        };
      }

      // Clean up the links (remove empty strings, normalize URLs)
      const cleanedLinks = this.cleanSocialLinks(socialLinks);

      const { data, error } = await supabase
        .from('profiles')
        .update({ 
          social_links: cleanedLinks,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
        .select('social_links')
        .single();

      if (error) {
        return {
          success: false,
          error: {
            code: 'UPDATE_ERROR',
            message: 'Failed to update social links',
            details: error
          }
        };
      }

      return {
        success: true,
        data: data.social_links as SocialLinks
      };

    } catch (error) {
      console.error('Update social links error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Get user's social links
   */
  async getSocialLinks(): Promise<SocialLinksResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('social_links')
        .eq('id', user.id)
        .single();

      if (error) {
        return {
          success: false,
          error: {
            code: 'FETCH_ERROR',
            message: 'Failed to fetch social links',
            details: error
          }
        };
      }

      // Ensure we return a complete SocialLinks object with defaults
      const socialLinks: SocialLinks = {
        twitter: '',
        instagram: '',
        linkedin: '',
        github: '',
        website: '',
        ...(data.social_links || {})
      };

      return {
        success: true,
        data: socialLinks
      };

    } catch (error) {
      console.error('Get social links error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Update profile information
   */
  async updateProfile(updates: Partial<ProfileData>): Promise<ProfileUpdateResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      // Remove fields that shouldn't be updated directly
      const { id, created_at, ...allowedUpdates } = updates;

      const { data, error } = await supabase
        .from('profiles')
        .update({
          ...allowedUpdates,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
        .select('*')
        .single();

      if (error) {
        return {
          success: false,
          error: {
            code: 'UPDATE_ERROR',
            message: 'Failed to update profile',
            details: error
          }
        };
      }

      return {
        success: true,
        data: data as ProfileData
      };

    } catch (error) {
      console.error('Update profile error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Validate social links URLs
   */
  private validateSocialLinks(socialLinks: SocialLinks): { valid: boolean; error?: string } {
    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    
    for (const [platform, url] of Object.entries(socialLinks)) {
      if (url && url.trim() !== '') {
        // Allow relative URLs or full URLs
        if (!url.startsWith('http') && !url.startsWith('/') && !urlPattern.test(url)) {
          return {
            valid: false,
            error: `Invalid URL format for ${platform}`
          };
        }
        
        // Platform-specific validation
        if (platform === 'twitter' && url && !this.isValidTwitterUrl(url)) {
          return {
            valid: false,
            error: 'Invalid Twitter URL format'
          };
        }
        
        if (platform === 'instagram' && url && !this.isValidInstagramUrl(url)) {
          return {
            valid: false,
            error: 'Invalid Instagram URL format'
          };
        }
        
        if (platform === 'linkedin' && url && !this.isValidLinkedInUrl(url)) {
          return {
            valid: false,
            error: 'Invalid LinkedIn URL format'
          };
        }
        
        if (platform === 'github' && url && !this.isValidGitHubUrl(url)) {
          return {
            valid: false,
            error: 'Invalid GitHub URL format'
          };
        }
      }
    }

    return { valid: true };
  }

  /**
   * Clean and normalize social links
   */
  private cleanSocialLinks(socialLinks: SocialLinks): SocialLinks {
    const cleaned: SocialLinks = {
      twitter: '',
      instagram: '',
      linkedin: '',
      github: '',
      website: ''
    };

    for (const [platform, url] of Object.entries(socialLinks)) {
      if (url && url.trim() !== '') {
        let cleanedUrl = url.trim();
        
        // Add https:// if no protocol specified
        if (!cleanedUrl.startsWith('http') && !cleanedUrl.startsWith('/')) {
          cleanedUrl = 'https://' + cleanedUrl;
        }
        
        cleaned[platform as keyof SocialLinks] = cleanedUrl;
      }
    }

    return cleaned;
  }

  /**
   * Platform-specific URL validators
   */
  private isValidTwitterUrl(url: string): boolean {
    return url.includes('twitter.com') || url.includes('x.com') || url.startsWith('@');
  }

  private isValidInstagramUrl(url: string): boolean {
    return url.includes('instagram.com') || url.startsWith('@');
  }

  private isValidLinkedInUrl(url: string): boolean {
    return url.includes('linkedin.com');
  }

  private isValidGitHubUrl(url: string): boolean {
    return url.includes('github.com');
  }
}

export const profileService = new ProfileService();
