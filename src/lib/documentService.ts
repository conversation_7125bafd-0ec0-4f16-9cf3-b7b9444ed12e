// Document Service
// Handles file uploads, document processing, and knowledge base management

import { supabase } from '@/integrations/supabase/client';

export interface DocumentMetadata {
  id: string;
  user_id: string;
  filename: string;
  original_name: string;
  file_size: number;
  file_type: string;
  storage_path: string;
  processed: boolean;
  content_preview?: string;
  tags?: string[];
  created_at: string;
  updated_at?: string;
}

export interface DocumentUploadResult {
  success: boolean;
  data?: DocumentMetadata;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

export interface DocumentListResult {
  success: boolean;
  data?: DocumentMetadata[];
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

class DocumentService {
  private readonly BUCKET_NAME = 'user-content';
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private readonly ALLOWED_TYPES = [
    'application/pdf',
    'text/plain',
    'text/markdown',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  /**
   * Upload a file to storage and save metadata
   */
  async uploadDocument(file: File): Promise<DocumentUploadResult> {
    try {
      // Validate file
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: validation.error || 'File validation failed'
          }
        };
      }

      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      // Generate unique filename
      const fileExtension = file.name.split('.').pop();
      const uniqueFilename = `${user.id}/${Date.now()}_${Math.random().toString(36).substring(7)}.${fileExtension}`;

      // Upload to storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(uniqueFilename, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        return {
          success: false,
          error: {
            code: 'UPLOAD_ERROR',
            message: 'Failed to upload file',
            details: uploadError
          }
        };
      }

      // Extract content preview
      const contentPreview = await this.extractContentPreview(file);

      // Save metadata to database
      const documentMetadata: Omit<DocumentMetadata, 'id' | 'created_at'> = {
        user_id: user.id,
        filename: uniqueFilename,
        original_name: file.name,
        file_size: file.size,
        file_type: file.type,
        storage_path: uploadData.path,
        processed: false,
        content_preview: contentPreview,
        tags: this.extractTags(file.name, contentPreview)
      };

      // For now, we'll store in user_settings as documents array
      // In a production app, you'd want a dedicated documents table
      const { data: existingSettings } = await supabase
        .from('user_settings')
        .select('documents')
        .eq('user_id', user.id)
        .single();

      const existingDocuments = existingSettings?.documents || [];
      const newDocument: DocumentMetadata = {
        id: crypto.randomUUID(),
        ...documentMetadata,
        created_at: new Date().toISOString()
      };

      const { error: saveError } = await supabase
        .from('user_settings')
        .upsert({
          user_id: user.id,
          documents: [...existingDocuments, newDocument]
        });

      if (saveError) {
        // Clean up uploaded file if metadata save fails
        await supabase.storage
          .from(this.BUCKET_NAME)
          .remove([uniqueFilename]);

        return {
          success: false,
          error: {
            code: 'METADATA_ERROR',
            message: 'Failed to save document metadata',
            details: saveError
          }
        };
      }

      return {
        success: true,
        data: newDocument
      };

    } catch (error) {
      console.error('Document upload error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Get all documents for the current user
   */
  async getUserDocuments(): Promise<DocumentListResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      const { data, error } = await supabase
        .from('user_settings')
        .select('documents')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        return {
          success: false,
          error: {
            code: 'FETCH_ERROR',
            message: 'Failed to fetch documents',
            details: error
          }
        };
      }

      return {
        success: true,
        data: data?.documents || []
      };

    } catch (error) {
      console.error('Get documents error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(documentId: string): Promise<{ success: boolean; error?: any }> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: { code: 'AUTH_ERROR', message: 'User not authenticated' }
        };
      }

      const { data } = await supabase
        .from('user_settings')
        .select('documents')
        .eq('user_id', user.id)
        .single();

      const documents = data?.documents || [];
      const documentToDelete = documents.find((doc: DocumentMetadata) => doc.id === documentId);
      
      if (!documentToDelete) {
        return {
          success: false,
          error: { code: 'NOT_FOUND', message: 'Document not found' }
        };
      }

      // Remove from storage
      await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([documentToDelete.filename]);

      // Update metadata
      const updatedDocuments = documents.filter((doc: DocumentMetadata) => doc.id !== documentId);
      
      const { error: updateError } = await supabase
        .from('user_settings')
        .update({ documents: updatedDocuments })
        .eq('user_id', user.id);

      if (updateError) {
        return {
          success: false,
          error: { code: 'UPDATE_ERROR', message: 'Failed to update document list', details: updateError }
        };
      }

      return { success: true };

    } catch (error) {
      console.error('Delete document error:', error);
      return {
        success: false,
        error: { code: 'UNKNOWN_ERROR', message: 'An unexpected error occurred', details: error }
      };
    }
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File): { valid: boolean; error?: string } {
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size exceeds ${this.MAX_FILE_SIZE / (1024 * 1024)}MB limit`
      };
    }

    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: 'File type not supported. Please upload PDF, TXT, MD, DOC, or DOCX files.'
      };
    }

    return { valid: true };
  }

  /**
   * Extract content preview from file
   */
  private async extractContentPreview(file: File): Promise<string> {
    try {
      if (file.type.startsWith('text/')) {
        const text = await file.text();
        return text.substring(0, 500) + (text.length > 500 ? '...' : '');
      }
      
      // For other file types, return basic info
      return `${file.type} file - ${file.name}`;
    } catch (error) {
      console.error('Content extraction error:', error);
      return `File: ${file.name}`;
    }
  }

  /**
   * Extract tags from filename and content
   */
  private extractTags(filename: string, content: string): string[] {
    const tags: string[] = [];
    
    // Extract from filename
    const nameWithoutExt = filename.split('.')[0].toLowerCase();
    if (nameWithoutExt.includes('resume') || nameWithoutExt.includes('cv')) {
      tags.push('resume');
    }
    if (nameWithoutExt.includes('project')) {
      tags.push('project');
    }
    if (nameWithoutExt.includes('report')) {
      tags.push('report');
    }

    // Extract from content (basic keyword matching)
    const contentLower = content.toLowerCase();
    if (contentLower.includes('experience') || contentLower.includes('skills')) {
      tags.push('professional');
    }
    if (contentLower.includes('education') || contentLower.includes('degree')) {
      tags.push('education');
    }

    return [...new Set(tags)]; // Remove duplicates
  }
}

export const documentService = new DocumentService();
