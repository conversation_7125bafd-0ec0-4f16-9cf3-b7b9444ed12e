// Knowledge Handlers
// Centralized handlers for Upload Documents, Social Links, and AI Personality features

import { documentService } from './documentService';
import { profileService } from './profileService';
import { settingsService } from './settingsService';

/**
 * Upload Documents Handlers
 */
export const documentHandlers = {
  /**
   * Upload multiple files
   */
  async uploadFiles(files: File[]) {
    const results = [];
    
    for (const file of files) {
      const result = await documentService.uploadDocument(file);
      results.push({
        file: file.name,
        success: result.success,
        data: result.data,
        error: result.error
      });
    }
    
    return results;
  },

  /**
   * Get all user documents
   */
  async getDocuments() {
    return await documentService.getUserDocuments();
  },

  /**
   * Delete a document
   */
  async deleteDocument(documentId: string) {
    return await documentService.deleteDocument(documentId);
  },

  /**
   * Bulk delete documents
   */
  async deleteDocuments(documentIds: string[]) {
    const results = [];
    
    for (const id of documentIds) {
      const result = await documentService.deleteDocument(id);
      results.push({
        documentId: id,
        success: result.success,
        error: result.error
      });
    }
    
    return results;
  }
};

/**
 * Social Links Handlers
 */
export const socialLinksHandlers = {
  /**
   * Get user's social links
   */
  async getSocialLinks() {
    return await profileService.getSocialLinks();
  },

  /**
   * Update social links
   */
  async updateSocialLinks(socialLinks: {
    twitter: string;
    instagram: string;
    linkedin: string;
    github: string;
    website: string;
  }) {
    return await profileService.updateSocialLinks(socialLinks);
  },

  /**
   * Validate social links before saving
   */
  validateSocialLinks(socialLinks: any) {
    const errors: string[] = [];
    
    // Basic URL validation
    Object.entries(socialLinks).forEach(([platform, url]) => {
      if (url && typeof url === 'string' && url.trim() !== '') {
        const trimmedUrl = url.trim();
        
        // Check if it's a valid URL format
        if (!trimmedUrl.startsWith('http') && !trimmedUrl.startsWith('/') && !trimmedUrl.includes('.')) {
          errors.push(`Invalid URL format for ${platform}`);
        }
      }
    });
    
    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Get profile data including social links
   */
  async getProfile() {
    return await profileService.getCurrentProfile();
  }
};

/**
 * AI Personality Handlers
 */
export const aiPersonalityHandlers = {
  /**
   * Get AI personality settings
   */
  async getPersonality() {
    return await settingsService.getAIPersonality();
  },

  /**
   * Update AI personality settings
   */
  async updatePersonality(personality: {
    tone?: 'friendly' | 'professional' | 'casual' | 'formal' | 'enthusiastic';
    style?: 'conversational' | 'informative' | 'creative' | 'analytical';
    personality?: string;
    response_length?: 'short' | 'medium' | 'long';
    knowledge_areas?: string[];
    custom_instructions?: string;
    greeting_message?: string;
  }) {
    return await settingsService.updateAIPersonality(personality);
  },

  /**
   * Get all user settings
   */
  async getAllSettings() {
    return await settingsService.getUserSettings();
  },

  /**
   * Update chat settings
   */
  async updateChatSettings(chatSettings: {
    auto_greeting?: boolean;
    response_delay?: number;
    typing_indicator?: boolean;
    conversation_memory?: boolean;
    context_awareness?: boolean;
  }) {
    return await settingsService.updateChatSettings(chatSettings);
  },

  /**
   * Reset all settings to defaults
   */
  async resetToDefaults() {
    return await settingsService.resetToDefaults();
  },

  /**
   * Validate personality settings
   */
  validatePersonality(personality: any) {
    const errors: string[] = [];
    
    if (personality.personality && personality.personality.length > 500) {
      errors.push('Personality description must be 500 characters or less');
    }
    
    if (personality.custom_instructions && personality.custom_instructions.length > 1000) {
      errors.push('Custom instructions must be 1000 characters or less');
    }
    
    const validTones = ['friendly', 'professional', 'casual', 'formal', 'enthusiastic'];
    if (personality.tone && !validTones.includes(personality.tone)) {
      errors.push('Invalid tone value');
    }
    
    const validStyles = ['conversational', 'informative', 'creative', 'analytical'];
    if (personality.style && !validStyles.includes(personality.style)) {
      errors.push('Invalid style value');
    }
    
    const validLengths = ['short', 'medium', 'long'];
    if (personality.response_length && !validLengths.includes(personality.response_length)) {
      errors.push('Invalid response length value');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
};

/**
 * Combined handlers for all knowledge features
 */
export const knowledgeHandlers = {
  documents: documentHandlers,
  socialLinks: socialLinksHandlers,
  aiPersonality: aiPersonalityHandlers,

  /**
   * Initialize all knowledge features
   */
  async initialize() {
    try {
      const [documents, socialLinks, personality] = await Promise.all([
        documentHandlers.getDocuments(),
        socialLinksHandlers.getSocialLinks(),
        aiPersonalityHandlers.getPersonality()
      ]);

      return {
        success: true,
        data: {
          documents: documents.success ? documents.data : [],
          socialLinks: socialLinks.success ? socialLinks.data : {},
          personality: personality.success ? personality.data : {}
        }
      };
    } catch (error) {
      console.error('Knowledge initialization error:', error);
      return {
        success: false,
        error: {
          code: 'INIT_ERROR',
          message: 'Failed to initialize knowledge features',
          details: error
        }
      };
    }
  },

  /**
   * Export all user data
   */
  async exportUserData() {
    try {
      const [documents, profile, settings] = await Promise.all([
        documentHandlers.getDocuments(),
        socialLinksHandlers.getProfile(),
        aiPersonalityHandlers.getAllSettings()
      ]);

      return {
        success: true,
        data: {
          documents: documents.data || [],
          profile: profile.data || {},
          settings: settings.data || {},
          exportedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Export error:', error);
      return {
        success: false,
        error: {
          code: 'EXPORT_ERROR',
          message: 'Failed to export user data',
          details: error
        }
      };
    }
  }
};

export default knowledgeHandlers;
