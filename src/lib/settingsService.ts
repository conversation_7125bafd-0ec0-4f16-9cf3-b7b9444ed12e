// Settings Service
// Handles user settings including AI personality configuration

import { supabase } from '@/integrations/supabase/client';

export interface AIPersonalitySettings {
  tone: 'friendly' | 'professional' | 'casual' | 'formal' | 'enthusiastic';
  style: 'conversational' | 'informative' | 'creative' | 'analytical';
  personality: string; // Custom personality description
  response_length: 'short' | 'medium' | 'long';
  knowledge_areas: string[];
  custom_instructions: string;
  greeting_message?: string;
}

export interface ChatSettings {
  auto_greeting: boolean;
  response_delay: number; // milliseconds
  typing_indicator: boolean;
  conversation_memory: boolean;
  context_awareness: boolean;
}

export interface UserSettings {
  user_id: string;
  ai_personality: AIPersonalitySettings;
  chat_settings: ChatSettings;
  language_preference?: string;
  theme_preference?: string;
  notification_settings?: any;
  created_at: string;
  updated_at?: string;
}

export interface SettingsResult {
  success: boolean;
  data?: UserSettings;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

export interface AIPersonalityResult {
  success: boolean;
  data?: AIPersonalitySettings;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

class SettingsService {
  /**
   * Get user's AI personality settings
   */
  async getAIPersonality(): Promise<AIPersonalityResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      const { data, error } = await supabase
        .from('user_settings')
        .select('ai_personality')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        return {
          success: false,
          error: {
            code: 'FETCH_ERROR',
            message: 'Failed to fetch AI personality settings',
            details: error
          }
        };
      }

      // Return default settings if none exist
      const defaultPersonality: AIPersonalitySettings = {
        tone: 'friendly',
        style: 'conversational',
        personality: 'helpful and engaging',
        response_length: 'medium',
        knowledge_areas: ['general'],
        custom_instructions: '',
        greeting_message: 'Hi! I\'m your AI assistant. How can I help you today?'
      };

      const aiPersonality = data?.ai_personality || defaultPersonality;

      return {
        success: true,
        data: aiPersonality
      };

    } catch (error) {
      console.error('Get AI personality error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Update user's AI personality settings
   */
  async updateAIPersonality(personality: Partial<AIPersonalitySettings>): Promise<AIPersonalityResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      // Validate personality settings
      const validation = this.validateAIPersonality(personality);
      if (!validation.valid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: validation.error || 'Invalid personality settings'
          }
        };
      }

      // Get current settings to merge with updates
      const currentResult = await this.getAIPersonality();
      if (!currentResult.success) {
        return currentResult;
      }

      const updatedPersonality = {
        ...currentResult.data,
        ...personality
      };

      // Upsert the settings
      const { data, error } = await supabase
        .from('user_settings')
        .upsert({
          user_id: user.id,
          ai_personality: updatedPersonality,
          updated_at: new Date().toISOString()
        })
        .select('ai_personality')
        .single();

      if (error) {
        return {
          success: false,
          error: {
            code: 'UPDATE_ERROR',
            message: 'Failed to update AI personality settings',
            details: error
          }
        };
      }

      return {
        success: true,
        data: data.ai_personality
      };

    } catch (error) {
      console.error('Update AI personality error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Get all user settings
   */
  async getUserSettings(): Promise<SettingsResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        return {
          success: false,
          error: {
            code: 'FETCH_ERROR',
            message: 'Failed to fetch user settings',
            details: error
          }
        };
      }

      // Return default settings if none exist
      const defaultSettings: UserSettings = {
        user_id: user.id,
        ai_personality: {
          tone: 'friendly',
          style: 'conversational',
          personality: 'helpful and engaging',
          response_length: 'medium',
          knowledge_areas: ['general'],
          custom_instructions: '',
          greeting_message: 'Hi! I\'m your AI assistant. How can I help you today?'
        },
        chat_settings: {
          auto_greeting: true,
          response_delay: 1000,
          typing_indicator: true,
          conversation_memory: true,
          context_awareness: true
        },
        created_at: new Date().toISOString()
      };

      const settings = data || defaultSettings;

      return {
        success: true,
        data: settings
      };

    } catch (error) {
      console.error('Get user settings error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Update chat settings
   */
  async updateChatSettings(chatSettings: Partial<ChatSettings>): Promise<SettingsResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      // Get current settings
      const currentResult = await this.getUserSettings();
      if (!currentResult.success) {
        return currentResult;
      }

      const updatedChatSettings = {
        ...currentResult.data!.chat_settings,
        ...chatSettings
      };

      const { data, error } = await supabase
        .from('user_settings')
        .upsert({
          user_id: user.id,
          chat_settings: updatedChatSettings,
          updated_at: new Date().toISOString()
        })
        .select('*')
        .single();

      if (error) {
        return {
          success: false,
          error: {
            code: 'UPDATE_ERROR',
            message: 'Failed to update chat settings',
            details: error
          }
        };
      }

      return {
        success: true,
        data: data
      };

    } catch (error) {
      console.error('Update chat settings error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Reset settings to defaults
   */
  async resetToDefaults(): Promise<SettingsResult> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return {
          success: false,
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        };
      }

      const defaultSettings = {
        user_id: user.id,
        ai_personality: {
          tone: 'friendly',
          style: 'conversational',
          personality: 'helpful and engaging',
          response_length: 'medium',
          knowledge_areas: ['general'],
          custom_instructions: '',
          greeting_message: 'Hi! I\'m your AI assistant. How can I help you today?'
        },
        chat_settings: {
          auto_greeting: true,
          response_delay: 1000,
          typing_indicator: true,
          conversation_memory: true,
          context_awareness: true
        },
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('user_settings')
        .upsert(defaultSettings)
        .select('*')
        .single();

      if (error) {
        return {
          success: false,
          error: {
            code: 'UPDATE_ERROR',
            message: 'Failed to reset settings',
            details: error
          }
        };
      }

      return {
        success: true,
        data: data
      };

    } catch (error) {
      console.error('Reset settings error:', error);
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: error
        }
      };
    }
  }

  /**
   * Validate AI personality settings
   */
  private validateAIPersonality(personality: Partial<AIPersonalitySettings>): { valid: boolean; error?: string } {
    const validTones = ['friendly', 'professional', 'casual', 'formal', 'enthusiastic'];
    const validStyles = ['conversational', 'informative', 'creative', 'analytical'];
    const validLengths = ['short', 'medium', 'long'];

    if (personality.tone && !validTones.includes(personality.tone)) {
      return { valid: false, error: 'Invalid tone value' };
    }

    if (personality.style && !validStyles.includes(personality.style)) {
      return { valid: false, error: 'Invalid style value' };
    }

    if (personality.response_length && !validLengths.includes(personality.response_length)) {
      return { valid: false, error: 'Invalid response length value' };
    }

    if (personality.personality && personality.personality.length > 500) {
      return { valid: false, error: 'Personality description too long (max 500 characters)' };
    }

    if (personality.custom_instructions && personality.custom_instructions.length > 1000) {
      return { valid: false, error: 'Custom instructions too long (max 1000 characters)' };
    }

    return { valid: true };
  }
}

export const settingsService = new SettingsService();
