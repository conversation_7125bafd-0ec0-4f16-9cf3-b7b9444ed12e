// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const DEFAULT_SUPABASE_URL = 'https://ixmiewujzdcfutkayrzt.supabase.co';
const DEFAULT_SUPABASE_PUBLISHABLE_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml4bWlld3VqemRjZnV0a2F5cnp0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4OTY5ODAsImV4cCI6MjA3MDQ3Mjk4MH0.QkOaN_4YDRZPn0nfpQ59vLfEfIVjPL2nWPKvypz9hnA';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || DEFAULT_SUPABASE_URL;
const SUPABASE_PUBLISHABLE_KEY =
  import.meta.env.VITE_SUPABASE_ANON_KEY || DEFAULT_SUPABASE_PUBLISHABLE_KEY;

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  },
  global: {
    headers: {
      'X-Client-Info': 'heey-aura-chat'
    }
  }
});

// Add error handling for auth state changes
supabase.auth.onAuthStateChange(async (event, session) => {
  console.log('Auth state changed:', event, session?.user?.id);

  if (event === 'SIGNED_OUT') {
    // Clear any cached data
    localStorage.removeItem('userAvatarUrl');
  }

  if (event === 'TOKEN_REFRESHED') {
    console.log('Token refreshed successfully');
  }

  if (event === 'SIGNED_IN' && session?.user) {
    console.log('User signed in successfully:', session.user.email);

    // Ensure user profile exists
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      if (error && error.code === 'PGRST116') {
        // Profile doesn't exist, create it
        console.log('Creating profile for new user');
        const { error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: session.user.id,
            username: session.user.email?.split('@')[0] || 'user',
            display_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
            avatar_url: session.user.user_metadata?.avatar_url,
            role: 'user'
          });

        if (insertError) {
          console.error('Error creating profile:', insertError);
        } else {
          console.log('Profile created successfully');
        }
      }
    } catch (error) {
      console.error('Error handling user profile:', error);
    }
  }

  if (event === 'SIGNED_IN' && session?.user?.app_metadata?.provider === 'google') {
    console.log('Google OAuth sign-in completed successfully');
  }
});
