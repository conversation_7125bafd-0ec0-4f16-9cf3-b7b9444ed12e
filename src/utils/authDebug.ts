// Authentication Debug Utilities
// Helper functions to debug and fix authentication issues

import { supabase } from '@/integrations/supabase/client';

export const clearAuthState = () => {
  console.log('Clearing authentication state...');
  
  // Clear localStorage
  localStorage.removeItem('sb-ixmiewujzdcfutkayrzt-auth-token');
  localStorage.removeItem('userAvatarUrl');
  localStorage.removeItem('user');
  
  // Clear sessionStorage
  sessionStorage.clear();
  
  // Sign out from Supabase
  supabase.auth.signOut();
  
  console.log('Authentication state cleared');
};

export const debugAuthState = async () => {
  console.log('=== Authentication Debug Info ===');
  
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log('Current session:', session);
    console.log('Session error:', sessionError);
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('Current user:', user);
    console.log('User error:', userError);
    
    // Check localStorage
    const authToken = localStorage.getItem('sb-ixmiewujzdcfutkayrzt-auth-token');
    console.log('Auth token in localStorage:', authToken ? 'Present' : 'Not found');
    
    // Test database connection
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    console.log('Database connection test:', { data, error });
    
  } catch (error) {
    console.error('Debug error:', error);
  }
  
  console.log('=== End Debug Info ===');
};

export const testLogin = async (email: string, password: string) => {
  console.log('Testing login with:', email);
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    console.log('Login result:', { data, error });
    
    if (data.user) {
      console.log('Login successful, user ID:', data.user.id);
      
      // Test profile access
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();
        
      console.log('Profile data:', { profile, profileError });
    }
    
    return { data, error };
  } catch (error) {
    console.error('Login test error:', error);
    return { data: null, error };
  }
};

// Add to window for easy debugging in browser console
if (typeof window !== 'undefined') {
  (window as any).authDebug = {
    clearAuthState,
    debugAuthState,
    testLogin
  };
}
